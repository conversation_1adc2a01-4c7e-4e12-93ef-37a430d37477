# 🌐 真正的多机分布式部署操作指南

## 📋 前提条件

### 网络环境
- **主机IP:** `***********` (有数据文件的电脑)
- **从机IP:** `192.168.1.x` (另一台电脑)
- **要求:** 两台电脑在同一局域网内，能够相互ping通

### 软件环境
两台电脑都需要安装：
```bash
pip install dask[complete] distributed dask-ml pyarrow fastparquet pandas numpy scikit-learn matplotlib seaborn plotly
```

### 数据文件准备
**重要：** 确保主机上有必要的数据文件：
```bash
# 检查数据文件是否存在
ls -la data/songs_processed.csv
# 或者
dir data\songs_processed.csv
```

如果数据文件不存在，请先运行数据预处理：
```bash
python data_preprocessing.py
```

---

## 🚀 分步操作指南

### 第一步：主机操作 (***********)

#### 1.1 启动Dask调度器
```bash
# 在主机上打开命令行，输入：
dask-scheduler --host 0.0.0.0 --port 8786 --dashboard-address 8787
```

**预期输出：**
```
distributed.scheduler - INFO - -----------------------------------------------
distributed.scheduler - INFO - Scheduler at: tcp://***********:8786
distributed.scheduler - INFO - dashboard at: http://***********:8787/status
distributed.scheduler - INFO - -----------------------------------------------
```

#### 1.2 验证调度器启动成功
- 浏览器访问：`http://***********:8787/status`
- 应该看到Dask仪表板界面
- **验证要点：**
  - 仪表板显示"Workers: 0"（暂时没有工作节点）
  - 调度器状态显示为"Running"
  - 没有错误信息

#### 1.3 网络连通性测试
在从机上测试网络连接：
```bash
# 测试主机连通性
ping ***********

# 测试端口连通性
telnet *********** 8786
# 如果telnet不可用，可以用：
# nc -zv *********** 8786
```

---

### 第二步：从机操作 (另一台电脑)

#### 2.1 连接到主机调度器
```bash
# 在从机上打开命令行，输入：
dask-worker tcp://************:8786 --memory-limit 2GB --nthreads 2
```

**预期输出：**
```
distributed.worker - INFO - Start worker at: tcp://192.168.1.x:xxxxx
distributed.worker - INFO - Registered to: tcp://***********:8786
distributed.worker - INFO - -----------------------------------------------
```

#### 2.2 验证工作节点连接成功
- 回到主机浏览器刷新仪表板
- 应该看到新增了一个工作节点
- **验证要点：**
  - 仪表板显示"Workers: 1"
  - 工作节点显示不同的IP地址
  - 内存和CPU资源显示正确
  - 工作节点状态为"Running"

#### 2.3 连接状态确认
在主机命令行中验证连接：
```bash
# 可选：使用Python快速测试连接
python -c "
from dask.distributed import Client
client = Client('tcp://***********:8786')
print('集群信息:', client)
print('工作节点:', len(client.scheduler_info()['workers']))
client.close()
"
```

---

### 第三步：主机运行分析 (***********)

#### 3.1 运行分布式分析
```bash
# 在主机上新开一个命令行窗口，输入：
python main_pipeline.py --mode multi-machine --scheduler tcp://***********:8786
```

#### 3.2 监控分析进度
- 仪表板地址：`http://***********:8787/status`
- 观察任务在不同机器间的分布情况
- **监控要点：**
  - 任务图显示任务在不同工作节点间分布
  - CPU和内存使用率在各节点间变化
  - 数据传输显示节点间通信
  - 进度条显示整体完成情况

#### 3.3 分析完成验证
分析完成后应该看到：
```
🎉 真正的分布式音乐数据分析完成!
📊 分布式分析摘要:
数据规模: 119,988 条记录 (全部12万条)
分布式特性: 真正多机分布式处理
工作节点: 2 个 (来自不同IP)
数据分区: X 个
集群仪表板: http://***********:8787/status
```

---

## � 第四步：正确停止分布式系统

### 停止顺序（重要！）

#### 4.1 停止分析进程
- 如果分析正在运行，按 `Ctrl+C` 停止

#### 4.2 停止工作节点（从机）
```bash
# 在从机命令行中按 Ctrl+C
# 应该看到：
distributed.worker - INFO - Stopping worker at tcp://192.168.1.x:xxxxx
```

#### 4.3 停止调度器（主机）
```bash
# 在主机调度器命令行中按 Ctrl+C
# 应该看到：
distributed.scheduler - INFO - Scheduler closing...
```

#### 4.4 验证完全停止
```bash
# 检查端口是否释放
netstat -an | grep 8786
netstat -an | grep 8787
# 应该没有输出，表示端口已释放
```

---

## �🔧 使用项目内置工具的方式

### 主机操作序列

#### 步骤1：启动调度器
```bash
python start_distributed.py --mode scheduler
```

#### 步骤2：运行分析
```bash
# 等从机连接后，在主机上执行：
python start_distributed.py --mode analysis --scheduler tcp://***********:8786
```

### 从机操作序列

#### 步骤1：连接工作节点
```bash
python start_distributed.py --mode worker --scheduler tcp://***********:8786
```

---

## 📊 完整的操作时序

### 时间线操作步骤

| 时间 | 主机 (***********) | 从机 (192.168.1.x) |
|------|-------------------|-------------------|
| T1 | `dask-scheduler --host 0.0.0.0 --port 8786 --dashboard-address 8787` | 等待 |
| T2 | 验证仪表板可访问 | `dask-worker tcp://***********:8786 --memory-limit 2GB --nthreads 2` |
| T3 | 确认工作节点已连接 | 保持工作节点运行 |
| T4 | `python main_pipeline.py --mode multi-machine --scheduler tcp://***********:8786` | 继续运行工作节点 |
| T5 | 监控分析进度 | 观察CPU/内存使用情况 |

---

## 🎯 验证真正分布式的标志

### 在仪表板中确认
1. **工作节点数量：** 显示 > 1 个工作节点
2. **不同IP地址：** 工作节点来自不同IP
3. **任务分布：** 任务在不同机器间分配
4. **内存使用：** 总内存 = 各机器内存之和

### 在系统监控中确认
```bash
# 主机监控 (主要负责数据I/O)
htop  # 查看CPU和内存使用

# 从机监控 (主要负责计算)
htop  # 应该看到计算任务
```

---

## ⚠️ 故障排除

### 如果从机连接失败

#### 检查网络连通性
```bash
# 在从机上测试：
ping ***********
telnet *********** 8786
```

#### 检查防火墙设置
```bash
# 在主机上执行：
netsh advfirewall firewall add rule name="Dask Scheduler" dir=in action=allow protocol=TCP localport=8786
netsh advfirewall firewall add rule name="Dask Dashboard" dir=in action=allow protocol=TCP localport=8787
```

### 如果分析运行失败

#### 常见错误及解决方案

**错误1：连接超时**
```
TimeoutError: timed out after connecting to tcp://***********:8786
```
解决方案：
- 检查调度器是否正常运行
- 确认IP地址正确
- 检查防火墙设置

**错误2：内存不足**
```
distributed.worker - ERROR - Worker exceeded memory limit
```
解决方案：
```bash
# 减少内存限制或增加工作节点
dask-worker tcp://***********:8786 --memory-limit 1GB --nthreads 1
```

**错误3：数据文件不存在**
```
FileNotFoundError: data/songs_processed.csv
```
解决方案：
```bash
# 运行数据预处理
python data_preprocessing.py
```

#### 回退到本地模式
```bash
# 在主机上执行：
python main_pipeline.py --mode local
```

#### 重新启动整个系统
```bash
# 1. 停止所有进程 (Ctrl+C)
# 2. 等待5秒
# 3. 重新按顺序启动
```

---

## 🎉 成功标志

当您看到以下输出时，说明多机分布式部署成功：

```
🎉 真正的分布式音乐数据分析完成!
📊 分布式分析摘要:
数据规模: 119,988 条记录 (全部12万条)
分布式特性: 真正多机分布式处理
工作节点: 2 个 (来自不同IP)
数据分区: X 个
集群仪表板: http://***********:8787/status
```

**恭喜！您已成功部署真正的多机分布式音乐数据分析系统！**

---

## 📋 完整操作检查清单

### 部署前检查
- [ ] 两台电脑在同一局域网
- [ ] 两台电脑都安装了必要的Python包
- [ ] 主机上存在数据文件 `data/songs_processed.csv`
- [ ] 防火墙允许端口8786和8787
- [ ] 网络连通性测试通过

### 部署过程检查
- [ ] 主机调度器启动成功
- [ ] 仪表板可以访问
- [ ] 从机工作节点连接成功
- [ ] 仪表板显示工作节点
- [ ] 分析程序运行成功
- [ ] 结果文件生成

### 部署后检查
- [ ] 分析结果正确
- [ ] 所有进程正常停止
- [ ] 端口释放完成
- [ ] 临时文件清理

---

## ⚡ 性能优化建议

### 硬件配置建议
```
主机 (数据存储):
- CPU: 4核以上
- 内存: 8GB以上
- 存储: SSD推荐

从机 (计算节点):
- CPU: 2核以上
- 内存: 4GB以上
- 网络: 千兆网卡
```

### 参数调优
```bash
# 高性能配置示例
# 主机调度器
dask-scheduler --host 0.0.0.0 --port 8786 --dashboard-address 8787

# 从机工作节点 (4GB内存机器)
dask-worker tcp://***********:8786 --memory-limit 3GB --nthreads 4 --nworkers 1

# 从机工作节点 (8GB内存机器)
dask-worker tcp://***********:8786 --memory-limit 6GB --nthreads 4 --nworkers 2
```

### 网络优化
- 使用有线网络连接
- 确保网络带宽充足
- 避免网络拥塞时段

---

## 🔄 扩展到更多机器

### 添加第三台机器
```bash
# 在第三台机器上执行：
dask-worker tcp://***********:8786 --memory-limit 2GB --nthreads 2
```

### 动态添加工作节点
- 可以在分析运行过程中添加新的工作节点
- 新节点会自动加入计算任务
- 仪表板会实时显示新节点

---

## 📚 相关文档

- [Dask官方文档](https://docs.dask.org/)
- [分布式计算最佳实践](https://distributed.dask.org/en/latest/best-practices.html)
- [项目GitHub仓库](https://github.com/your-repo)

---

## 🆘 获取帮助

如果遇到问题：
1. 查看本指南的故障排除部分
2. 检查Dask仪表板的错误信息
3. 查看命令行输出的错误日志
4. 尝试本地模式验证代码正确性
