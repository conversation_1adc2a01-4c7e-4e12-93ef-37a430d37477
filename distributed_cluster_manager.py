#!/usr/bin/env python3
"""
真正的多机分布式集群管理器
支持一键启动分布式Dask集群，实现真正的多机分布式部署
"""

import argparse
import subprocess
import sys
import time
import socket
import logging
from pathlib import Path
import psutil
import json

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DistributedClusterManager:
    """分布式集群管理器"""
    
    def __init__(self):
        self.scheduler_port = 8786
        self.dashboard_port = 8787
        self.worker_processes = []
        
    def get_local_ip(self):
        """获取本机IP地址"""
        try:
            # 连接到外部地址来获取本机IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except:
            return "127.0.0.1"
    
    def check_port_available(self, port):
        """检查端口是否可用"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('', port))
                return True
            except:
                return False
    
    def start_scheduler(self, host="0.0.0.0"):
        """启动Dask调度器"""
        logger.info("🚀 启动Dask调度器...")
        
        # 检查端口是否可用
        if not self.check_port_available(self.scheduler_port):
            logger.error(f"❌ 端口 {self.scheduler_port} 已被占用")
            return False
            
        if not self.check_port_available(self.dashboard_port):
            logger.error(f"❌ 端口 {self.dashboard_port} 已被占用")
            return False
        
        # 启动调度器
        cmd = [
            "dask-scheduler",
            "--host", host,
            "--port", str(self.scheduler_port),
            "--dashboard-address", str(self.dashboard_port)
        ]
        
        try:
            logger.info(f"执行命令: {' '.join(cmd)}")
            process = subprocess.Popen(cmd)
            
            # 等待调度器启动
            time.sleep(3)
            
            local_ip = self.get_local_ip()
            logger.info(f"✅ Dask调度器已启动")
            logger.info(f"📊 调度器地址: tcp://{local_ip}:{self.scheduler_port}")
            logger.info(f"🌐 仪表板地址: http://{local_ip}:{self.dashboard_port}/status")
            logger.info(f"💡 在其他机器上运行工作节点:")
            logger.info(f"   python {__file__} --mode worker --scheduler tcp://{local_ip}:{self.scheduler_port}")
            
            return process
            
        except Exception as e:
            logger.error(f"❌ 启动调度器失败: {e}")
            return False
    
    def start_worker(self, scheduler_address, memory_limit="2GB", nthreads=2, nworkers=1):
        """启动Dask工作节点"""
        logger.info(f"🔧 启动Dask工作节点...")
        logger.info(f"📡 连接到调度器: {scheduler_address}")
        
        cmd = [
            "dask-worker",
            scheduler_address,
            "--memory-limit", memory_limit,
            "--nthreads", str(nthreads),
            "--nworkers", str(nworkers)
        ]
        
        try:
            logger.info(f"执行命令: {' '.join(cmd)}")
            process = subprocess.Popen(cmd)
            
            # 等待工作节点启动
            time.sleep(2)
            
            logger.info(f"✅ Dask工作节点已启动")
            logger.info(f"💾 内存限制: {memory_limit}")
            logger.info(f"🧵 线程数: {nthreads}")
            logger.info(f"👷 工作进程数: {nworkers}")
            
            return process
            
        except Exception as e:
            logger.error(f"❌ 启动工作节点失败: {e}")
            return False
    
    def get_system_info(self):
        """获取系统信息"""
        info = {
            "CPU核心数": psutil.cpu_count(),
            "物理CPU核心数": psutil.cpu_count(logical=False),
            "总内存": f"{psutil.virtual_memory().total / 1024**3:.1f} GB",
            "可用内存": f"{psutil.virtual_memory().available / 1024**3:.1f} GB",
            "本机IP": self.get_local_ip()
        }
        return info
    
    def print_deployment_guide(self):
        """打印部署指南"""
        local_ip = self.get_local_ip()
        system_info = self.get_system_info()
        
        print(f"""
🌐 真正的多机分布式部署指南
==========================================

📊 当前机器信息:
  IP地址: {system_info['本机IP']}
  CPU核心: {system_info['CPU核心数']} ({system_info['物理CPU核心数']} 物理核心)
  总内存: {system_info['总内存']}
  可用内存: {system_info['可用内存']}

🚀 快速部署步骤:

1️⃣ 主机 (当前机器) - 启动调度器:
   python {__file__} --mode scheduler

2️⃣ 从机 (其他机器) - 启动工作节点:
   python {__file__} --mode worker --scheduler tcp://{local_ip}:{self.scheduler_port}

3️⃣ 运行分析 (在主机):
   python main_pipeline.py --mode multi-machine --scheduler tcp://{local_ip}:{self.scheduler_port}

📊 监控地址:
   http://{local_ip}:{self.dashboard_port}/status

💡 高性能配置示例:
   # 主机 (4核8GB)
   python {__file__} --mode scheduler
   
   # 从机 (2核4GB)
   python {__file__} --mode worker --scheduler tcp://{local_ip}:{self.scheduler_port} --memory 3GB --nthreads 2

🔧 自定义配置:
   --memory: 内存限制 (如: 2GB, 4GB)
   --nthreads: 每个工作进程的线程数
   --nworkers: 工作进程数量

==========================================
        """)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="真正的多机分布式集群管理器")
    parser.add_argument("--mode", type=str, choices=['scheduler', 'worker', 'guide'], 
                       default='guide', help="运行模式")
    parser.add_argument("--scheduler", type=str, default=None,
                       help="调度器地址 (worker模式必需)")
    parser.add_argument("--memory", type=str, default="2GB",
                       help="工作节点内存限制")
    parser.add_argument("--nthreads", type=int, default=2,
                       help="每个工作进程的线程数")
    parser.add_argument("--nworkers", type=int, default=1,
                       help="工作进程数量")
    parser.add_argument("--host", type=str, default="0.0.0.0",
                       help="调度器监听地址")
    
    args = parser.parse_args()
    
    manager = DistributedClusterManager()
    
    if args.mode == 'guide':
        manager.print_deployment_guide()
        return
    
    elif args.mode == 'scheduler':
        logger.info("🎯 启动分布式调度器模式")
        process = manager.start_scheduler(args.host)
        if process:
            try:
                # 保持调度器运行
                logger.info("⏳ 调度器运行中... (按 Ctrl+C 停止)")
                process.wait()
            except KeyboardInterrupt:
                logger.info("🛑 正在停止调度器...")
                process.terminate()
                process.wait()
                logger.info("✅ 调度器已停止")
    
    elif args.mode == 'worker':
        if not args.scheduler:
            logger.error("❌ worker模式需要指定 --scheduler 参数")
            sys.exit(1)
        
        logger.info("🎯 启动分布式工作节点模式")
        process = manager.start_worker(
            args.scheduler, 
            args.memory, 
            args.nthreads, 
            args.nworkers
        )
        if process:
            try:
                # 保持工作节点运行
                logger.info("⏳ 工作节点运行中... (按 Ctrl+C 停止)")
                process.wait()
            except KeyboardInterrupt:
                logger.info("🛑 正在停止工作节点...")
                process.terminate()
                process.wait()
                logger.info("✅ 工作节点已停止")

if __name__ == "__main__":
    main()
