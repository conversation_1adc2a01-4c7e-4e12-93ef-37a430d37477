#!/usr/bin/env python3
"""
一键启动真正的分布式音乐数据分析系统
支持本地多进程和真正的多机分布式部署
"""

import argparse
import subprocess
import sys
import time
import os
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查依赖项"""
    logger.info("🔍 检查系统依赖...")
    
    required_packages = [
        'dask', 'distributed', 'dask_ml', 'pandas', 'numpy', 
        'matplotlib', 'plotly', 'pyarrow', 'fastparquet'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        logger.info("💡 请运行: pip install dask[complete] distributed dask-ml pandas numpy matplotlib plotly pyarrow fastparquet")
        return False
    
    logger.info("✅ 所有依赖项已安装")
    return True

def check_data_file():
    """检查数据文件"""
    data_files = [
        "data/songs_processed.csv",
        "songs_processed.csv"
    ]
    
    for file_path in data_files:
        if Path(file_path).exists():
            logger.info(f"✅ 找到数据文件: {file_path}")
            return file_path
    
    logger.error("❌ 未找到数据文件 songs_processed.csv")
    logger.info("💡 请确保数据文件存在于 data/ 目录或当前目录")
    return None

def start_local_distributed():
    """启动本地分布式模式"""
    logger.info("🚀 启动本地分布式模式...")
    
    # 检查依赖和数据
    if not check_dependencies():
        return False
    
    data_file = check_data_file()
    if not data_file:
        return False
    
    # 运行分析
    cmd = ["python", "main_pipeline.py", "--input", data_file, "--mode", "local"]
    
    logger.info(f"执行命令: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, check=True)
        logger.info("✅ 本地分布式分析完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ 分析失败: {e}")
        return False

def start_multi_machine_scheduler():
    """启动多机分布式调度器"""
    logger.info("🌐 启动多机分布式调度器...")
    
    if not check_dependencies():
        return False
    
    # 启动集群管理器
    cmd = ["python", "distributed_cluster_manager.py", "--mode", "scheduler"]
    
    logger.info(f"执行命令: {' '.join(cmd)}")
    try:
        subprocess.run(cmd)
        return True
    except KeyboardInterrupt:
        logger.info("🛑 调度器已停止")
        return True
    except Exception as e:
        logger.error(f"❌ 启动调度器失败: {e}")
        return False

def start_multi_machine_worker(scheduler_address):
    """启动多机分布式工作节点"""
    logger.info(f"🔧 启动多机分布式工作节点，连接到: {scheduler_address}")
    
    if not check_dependencies():
        return False
    
    # 启动工作节点
    cmd = ["python", "distributed_cluster_manager.py", "--mode", "worker", "--scheduler", scheduler_address]
    
    logger.info(f"执行命令: {' '.join(cmd)}")
    try:
        subprocess.run(cmd)
        return True
    except KeyboardInterrupt:
        logger.info("🛑 工作节点已停止")
        return True
    except Exception as e:
        logger.error(f"❌ 启动工作节点失败: {e}")
        return False

def run_multi_machine_analysis(scheduler_address):
    """运行多机分布式分析"""
    logger.info(f"🤖 运行多机分布式分析，连接到: {scheduler_address}")
    
    # 检查依赖和数据
    if not check_dependencies():
        return False
    
    data_file = check_data_file()
    if not data_file:
        return False
    
    # 运行分析
    cmd = [
        "python", "main_pipeline.py", 
        "--input", data_file,
        "--mode", "multi-machine",
        "--scheduler", scheduler_address
    ]
    
    logger.info(f"执行命令: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, check=True)
        logger.info("✅ 多机分布式分析完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ 分析失败: {e}")
        return False

def print_usage_guide():
    """打印使用指南"""
    print("""
🚀 真正的分布式音乐数据分析系统 - 一键启动指南
=======================================================

📋 使用方式:

1️⃣ 本地分布式模式 (单机多进程):
   python start_distributed.py --mode local

2️⃣ 多机分布式模式:
   
   主机 (启动调度器):
   python start_distributed.py --mode scheduler
   
   从机 (启动工作节点):
   python start_distributed.py --mode worker --scheduler tcp://主机IP:8786
   
   主机 (运行分析):
   python start_distributed.py --mode analysis --scheduler tcp://主机IP:8786

3️⃣ 查看部署指南:
   python distributed_cluster_manager.py

🎯 推荐流程:

单机测试:
  python start_distributed.py --mode local

多机部署:
  1. 主机: python start_distributed.py --mode scheduler
  2. 从机: python start_distributed.py --mode worker --scheduler tcp://主机IP:8786  
  3. 主机: python start_distributed.py --mode analysis --scheduler tcp://主机IP:8786

📊 特点:
  ✅ 真正的全分布式: 数据存储、预处理、分析全程分布式
  ✅ 多机支持: 支持真正的多机分布式部署
  ✅ 一键启动: 简化的启动流程
  ✅ 自动检查: 自动检查依赖和数据文件

=======================================================
    """)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="一键启动真正的分布式音乐数据分析系统")
    parser.add_argument("--mode", type=str, 
                       choices=['local', 'scheduler', 'worker', 'analysis', 'guide'],
                       default='guide',
                       help="运行模式")
    parser.add_argument("--scheduler", type=str, default=None,
                       help="调度器地址 (如: tcp://*************:8786)")
    
    args = parser.parse_args()
    
    if args.mode == 'guide':
        print_usage_guide()
        return
    
    elif args.mode == 'local':
        logger.info("🎯 启动本地分布式模式")
        success = start_local_distributed()
        if success:
            logger.info("🎉 本地分布式分析完成!")
        else:
            logger.error("❌ 本地分布式分析失败")
            sys.exit(1)
    
    elif args.mode == 'scheduler':
        logger.info("🎯 启动多机分布式调度器模式")
        start_multi_machine_scheduler()
    
    elif args.mode == 'worker':
        if not args.scheduler:
            logger.error("❌ worker模式需要指定 --scheduler 参数")
            logger.info("💡 示例: python start_distributed.py --mode worker --scheduler tcp://*************:8786")
            sys.exit(1)
        
        logger.info("🎯 启动多机分布式工作节点模式")
        start_multi_machine_worker(args.scheduler)
    
    elif args.mode == 'analysis':
        if not args.scheduler:
            logger.error("❌ analysis模式需要指定 --scheduler 参数")
            logger.info("💡 示例: python start_distributed.py --mode analysis --scheduler tcp://*************:8786")
            sys.exit(1)
        
        logger.info("🎯 启动多机分布式分析模式")
        success = run_multi_machine_analysis(args.scheduler)
        if success:
            logger.info("🎉 多机分布式分析完成!")
        else:
            logger.error("❌ 多机分布式分析失败")
            sys.exit(1)

if __name__ == "__main__":
    main()
