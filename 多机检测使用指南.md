# 多机分布式检测使用指南

## 📋 概述

本指南介绍如何使用多机分布式检测工具来验证您的 Dask 集群是否实现了真正的多机分布式部署。

## 🔧 检测工具

### 主要检测脚本
- **`端口检测.py`** - 全面的多机分布式检测工具

### 功能特性
- ✅ 系统信息检查
- ✅ Dask 安装状态验证
- ✅ 网络连通性测试
- ✅ 仪表板访问检查
- ✅ 工作节点分析
- ✅ 多机部署判断
- ✅ 性能测试
- ✅ 检测报告生成

## 🚀 使用方法

### 1. 基本检测

```bash
# 使用默认调度器地址检测
python 端口检测.py

# 指定调度器地址检测
python 端口检测.py --scheduler tcp://*************:8786
```

### 2. 全面检测（推荐）

```bash
# 全面检测 + 性能测试 + 保存报告
python 端口检测.py --scheduler tcp://*************:8786 --performance-test --save-report
```

### 3. 简单检测

```bash
# 快速简单检测
python 端口检测.py --scheduler tcp://*************:8786 --simple
```

## 📊 检测项目说明

### 1. 系统信息检查
- CPU 核心数（逻辑/物理）
- 内存总量和可用量
- CPU 和内存使用率
- 本机 IP 地址

### 2. Dask 安装检查
- Dask 是否正确安装
- Dask 版本信息
- 分布式组件版本

### 3. 网络连通性检查
- 调度器端口（8786）连通性
- 仪表板端口（8787）连通性
- 网络延迟测试

### 4. 集群状态检查
- 工作节点数量
- 工作节点 IP 分布
- 每个节点的线程数和内存限制
- 集群总计算资源

### 5. 多机判断逻辑
- **真正多机**: 工作节点分布在不同 IP 地址
- **单机分布式**: 所有工作节点在同一台机器

### 6. 性能测试（可选）
- 分布式矩阵计算测试
- 计算时间和性能指标
- 集群协调能力验证

## 🎯 检测结果解读

### ✅ 多机分布式成功标志
```
🎉 ✅ 真正的多机分布式部署
🚀 可以充分利用多台机器的计算资源
⭐ 高性能配置，适合大规模数据处理
```

### ❌ 单机分布式标志
```
⚠️ ❌ 单机分布式部署
💡 所有工作节点都在本机或调度器机器上
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 调度器不可达
```
❌ 调度器不可达！
```
**解决方案:**
- 确保调度器已启动: `dask-scheduler --host 0.0.0.0 --port 8786`
- 检查 IP 地址是否正确
- 确认防火墙允许 8786 和 8787 端口
- 测试网络连通性: `ping 调度器IP`

#### 2. 没有工作节点
```
⚠️ 没有发现工作节点
```
**解决方案:**
- 在其他机器启动工作节点: `dask-worker tcp://调度器IP:8786 --memory-limit 2GB --nthreads 2`
- 检查工作节点是否成功连接到调度器
- 查看调度器日志确认连接状态

#### 3. 仪表板不可访问
```
❌ 仪表板不可访问
```
**解决方案:**
- 确保调度器启动时指定了仪表板地址: `--dashboard-address 8787`
- 检查防火墙是否允许 8787 端口
- 尝试直接访问: `http://调度器IP:8787/status`

#### 4. Dask 未安装
```
❌ Dask未安装
```
**解决方案:**
```bash
pip install dask[distributed] dask[complete]
```

## 📈 性能优化建议

### 1. 高性能配置
- **多机器**: 至少 2 台不同的物理机器
- **多核心**: 每台机器至少 4 个 CPU 核心
- **充足内存**: 每台机器至少 8GB 内存
- **网络**: 千兆以太网连接

### 2. 工作节点配置
```bash
# 高性能配置示例
dask-worker tcp://调度器IP:8786 \
  --memory-limit 6GB \
  --nthreads 4 \
  --nworkers 1
```

### 3. 调度器配置
```bash
# 调度器优化配置
dask-scheduler \
  --host 0.0.0.0 \
  --port 8786 \
  --dashboard-address 8787 \
  --scheduler-file scheduler.json
```

## 📄 报告文件

检测工具会生成以下文件：
- `multi_machine_detection_report.json` - 详细检测报告
- 包含所有检测项目的结果和系统信息

## 🔄 定期检测建议

建议定期运行检测工具：
- **部署后**: 验证初始配置
- **添加节点后**: 确认新节点正常工作
- **性能问题时**: 诊断集群状态
- **定期维护**: 每周检查集群健康状态

## 💡 最佳实践

1. **网络配置**: 确保所有机器在同一网络段
2. **防火墙设置**: 开放必要端口（8786, 8787）
3. **资源监控**: 定期检查各节点资源使用情况
4. **负载均衡**: 合理分配工作节点资源
5. **故障恢复**: 准备节点故障的应急方案

## 📞 技术支持

如果遇到问题，请：
1. 运行完整检测并保存报告
2. 检查系统日志和 Dask 日志
3. 确认网络和防火墙配置
4. 参考 Dask 官方文档

---

**注意**: 真正的多机分布式需要至少 2 台不同的物理机器，单机多进程不算多机分布式。
