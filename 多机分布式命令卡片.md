# 🎯 多机分布式命令卡片

## 📱 主机命令 (***********)

### 第1步：启动调度器
```bash
dask-scheduler --host 0.0.0.0 --port 8786 --dashboard-address 8787
```

### 第2步：验证仪表板
```
浏览器访问: http://***********:8787/status
```

### 第3步：运行分析 (新开命令行窗口)
```bash
python main_pipeline.py --mode multi-machine --scheduler tcp://***********:8786
```

---

## 💻 从机命令 (另一台电脑)

### 连接工作节点
```bash
dask-worker tcp://***********:8786 --memory-limit 2GB --nthreads 2
```

---

## 🛠️ 使用项目工具的方式

### 主机
```bash
# 1. 启动调度器
python start_distributed.py --mode scheduler

# 2. 运行分析 (等从机连接后)
python start_distributed.py --mode analysis --scheduler tcp://***********:8786
```

### 从机
```bash
# 连接工作节点
python start_distributed.py --mode worker --scheduler tcp://***********:8786
```

---

## 🔍 快速检查

### 网络测试 (在从机上)
```bash
ping ***********
telnet *********** 8786
```

### 防火墙设置 (在主机上)
```bash
netsh advfirewall firewall add rule name="Dask Scheduler" dir=in action=allow protocol=TCP localport=8786
netsh advfirewall firewall add rule name="Dask Dashboard" dir=in action=allow protocol=TCP localport=8787
```

### 备用方案 (本地模式)
```bash
python main_pipeline.py --mode local
```

---

## ✅ 成功标志

- 仪表板显示多个工作节点
- 工作节点来自不同IP地址
- 分析输出显示"真正多机分布式处理"
- 任务在不同机器间分布执行

---

## 📋 操作顺序

1. **主机:** 启动调度器
2. **主机:** 验证仪表板可访问
3. **从机:** 连接工作节点
4. **主机:** 确认工作节点已连接
5. **主机:** 运行分布式分析
6. **两机:** 监控执行进度
