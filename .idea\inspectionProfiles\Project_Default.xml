<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="16">
            <item index="0" class="java.lang.String" itemvalue="auto-gptq" />
            <item index="1" class="java.lang.String" itemvalue="einops" />
            <item index="2" class="java.lang.String" itemvalue="swift" />
            <item index="3" class="java.lang.String" itemvalue="accelerator" />
            <item index="4" class="java.lang.String" itemvalue="peft" />
            <item index="5" class="java.lang.String" itemvalue="gc" />
            <item index="6" class="java.lang.String" itemvalue="gensim" />
            <item index="7" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="8" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="9" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="10" class="java.lang.String" itemvalue="numpy" />
            <item index="11" class="java.lang.String" itemvalue="Flask-SQLAlchemy" />
            <item index="12" class="java.lang.String" itemvalue="Flask" />
            <item index="13" class="java.lang.String" itemvalue="pandas" />
            <item index="14" class="java.lang.String" itemvalue="torch" />
            <item index="15" class="java.lang.String" itemvalue="matplotlib" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>